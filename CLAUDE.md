# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Alfred is an AI-powered log analysis assistant that uses Google GenAI to analyze application logs from microservices. It's built with Python/Flask and uses LangGraph for agent orchestration.

## Development Commands

### Environment Setup
```bash
# Activate virtual environment first
source .venv/bin/activate

# Install dependencies
uv pip install -r requirements.txt

# Set up pre-commit hooks
pre-commit install
```

### Running the Application
```bash
# Development mode with UI
python run.py

# CLI mode
python -m src

# Skip tests on startup (faster development)
SKIP_TESTS=true python run.py

# Production mode with Gunicorn
gunicorn -c gunicorn_config.py main:app

# Using Docker
docker-compose up -d
```

### Testing
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/unit/test_embedding_service.py

# Run with coverage
pytest --cov=src --cov=app tests/
```

### Code Quality
```bash
# Format code with Black
black . --line-length 88

# Sort imports
isort .

# Run pre-commit checks
pre-commit run --all-files
```

- Use simple git messages

## Architecture Overview

### Core Components

1. **Agent System** (`src/log_agent.py`)
   - LangGraph-based agent that orchestrates log analysis
   - Integrates multiple tools (MongoDB queries, search, etc.)
   - Handles complex multi-step analysis tasks

2. **Memory System** (`src/memory/`)
   - `memory_manager.py`: Manages context and conversation history
   - `embedding_service.py`: Handles text embeddings for semantic search
   - Uses Chroma for vector storage

3. **Database Layer** (`src/database_manager.py`)
   - MongoDB integration for log storage and retrieval
   - Handles connection pooling and query optimization
   - Supports both sync and async operations

4. **Web Application** (`app/`)
   - Flask routes in `app/routes.py`
   - Session management for chat history
   - Background task processing for log summarization

5. **Log Processing** (`src/summarize_logs/`)
   - Automated log summarization
   - Pub/Sub integration for real-time log ingestion
   - Batch processing capabilities

### Key Design Patterns

- **Environment-based Configuration**: Uses `.env.prod`, `.env.uat`, `.env.test` files
- **Secret Management**: Integrates with GCP Secret Manager for sensitive data
- **Async Processing**: Background tasks for expensive operations
- **Caching**: Implements caching layer for frequently accessed data
- **Tool Integration**: Extensible tool system for adding new analysis capabilities

### Data Flow

1. Logs ingested via Pub/Sub or direct upload
2. Stored in MongoDB with metadata
3. Background processing creates summaries and embeddings
4. User queries processed by LangGraph agent
5. Agent uses tools to analyze logs and generate insights
6. Results cached and returned via Flask API

## Important Notes

- Always activate the virtual environment before running Python commands
- The project uses Black with line-length 88 for code formatting
- MongoDB must be running (either locally or via Docker)
- Google Cloud credentials required for GenAI integration
- Environment variables must be set according to the target environment